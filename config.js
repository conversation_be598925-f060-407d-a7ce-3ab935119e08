require('dotenv').config();

const config = {
  // Threads API Configuration
  threads: {
    apiUrl: 'https://www.threads.com/graphql/query',
    headers: {
      'accept': '*/*',
      'accept-language': 'en-US,en;q=0.9,ro;q=0.8',
      'content-type': 'application/x-www-form-urlencoded',
      'origin': 'https://www.threads.com',
      'priority': 'u=1, i',
      'referer': 'https://www.threads.com/',
      'sec-ch-prefers-color-scheme': 'dark',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-full-version-list': '"Google Chrome";v="137.0.7151.69", "Chromium";v="137.0.7151.69", "Not/A)Brand";v="24.0.0.0"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-model': '""',
      'sec-ch-ua-platform': '"macOS"',
      'sec-ch-ua-platform-version': '"15.2.0"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
      'x-asbd-id': '359341',
      'x-bloks-version-id': 'd76e71786ecd1b115d8db5725ebf6e695a1b7c899b17fe27d8b50d4707ee022f',
      'x-fb-friendly-name': 'BarcelonaFeedPaginationDirectQuery',
      'x-fb-lsd': 'EVbdoebc_eC2ziHDPpdwCI',
      'x-ig-app-id': '238260118697367',
      'x-root-field-name': 'xdt_api__v1__feed__text_post_app_timeline__connection'
    },
    // Authentication - set these in your .env file
    cookies: process.env.THREADS_COOKIES || 'cb=1_2025_06_07_; mid=aERk1wAEAAFntf00XXmsi3i-lLYK; ig_did=DE93F21A-EB48-4C3B-B5D4-76B78A24434E; csrftoken=4w2XtJVnDTA7yzC8YntoJ6CpqWas3d25; ds_user_id=63483289401; sessionid=63483289401%3AzjDrfwBklBAeYn%3A29%3AAYeqXHeHEvIuz8_al_F13e8_RvpSCvQObD9XHrvl6A; rur="NCG\\05463483289401\\0541780852662:01fee19de25f0da8e57d1bfbecc6cda75f079b3b2086af25e4ddf424d34852fc1c7c86e8"; csrftoken=4w2XtJVnDTA7yzC8YntoJ6CpqWas3d25; ds_user_id=63483289401; rur="NCG\\05463483289401\\0541780853324:01fe0fa7c46f2c6e2e0dbfd58f4519913faf2553f9a2fb7c2409e616618b6f35b3a7e8cc"',
    csrfToken: process.env.THREADS_CSRF_TOKEN || '4w2XtJVnDTA7yzC8YntoJ6CpqWas3d25'
  },

  // LM Studio Configuration
  lmStudio: {
    url: 'http://127.0.0.1:1234/v1/chat/completions',
    model: 'qwen/qwen3-32b',
    maxTokens: 1000,
    temperature: 0.3
  },

  // Lead Scoring Configuration
  scoring: {
    threshold: 65, // Minimum score to qualify as a lead (lowered to catch more opportunities)
    weights: {
      urgency: 0.3,
      obtainability: 0.3,
      general: 0.4
    }
  },

  // Storage Configuration
  storage: {
    leadsFile: './data/leads.json',
    processedPostsFile: './data/processed_posts.json',
    configFile: './data/config.json'
  },

  // Notification Configuration
  notifications: {
    enabled: true,
    title: 'New High-Quality Lead Found!',
    timeout: 10000, // 10 seconds
    sound: true
  },

  // Processing Configuration
  processing: {
    maxPostsPerRequest: 20, // Increased for better batch filtering
    requestDelay: 2000, // 2 seconds between requests
    cronSchedule: '*/15 * * * *', // Every 15 minutes for production
    maxRetries: 3,
    maxCyclesPerRun: 8, // Maximum cycles to prevent infinite loops
    delayBetweenCycles: 5000, // 5 seconds between cycles when no leads found
    individualScoringDelay: 3000 // 3 seconds between individual post scoring
  }
};

module.exports = config;
