#!/usr/bin/env node

const ThreadsLeadGenerator = require('./main');

async function startProduction() {
  console.log('🚀 Starting Threads Lead Generation System - Production Mode\n');
  
  const generator = new ThreadsLeadGenerator();
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Received shutdown signal...');
    await generator.shutdown();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\n🛑 Received termination signal...');
    await generator.shutdown();
    process.exit(0);
  });

  try {
    // Initialize system
    console.log('🔧 Initializing system...');
    const initialized = await generator.initialize();
    
    if (!initialized) {
      console.error('❌ Failed to initialize system');
      process.exit(1);
    }

    console.log('✅ System initialized successfully\n');

    // Show current configuration
    console.log('📋 Production Configuration:');
    console.log('   • Lead threshold: 75/100 points');
    console.log('   • Processing frequency: Every 15 minutes');
    console.log('   • Max posts per batch: 20');
    console.log('   • Max cycles per run: 8');
    console.log('   • LM Studio endpoint: http://127.0.0.1:1234\n');

    // Start automated processing
    console.log('⏰ Starting automated lead hunting...');
    const cronJob = generator.startAutomatedProcessing();
    
    console.log('🎯 System is now running in production mode!');
    console.log('📊 Use Ctrl+C to stop gracefully\n');
    
    // Run once immediately to test
    console.log('🔄 Running initial lead hunt...');
    await generator.runOnce();
    
    // Keep process alive
    console.log('\n✅ Production system active - monitoring for leads...');
    
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Start production system
startProduction();
