#!/usr/bin/env node

const fs = require('fs-extra');
const config = require('./config');

async function showStatus() {
  console.log('📊 Threads Lead Generation System - Status Report\n');

  try {
    // Check if data files exist
    const leadsExist = await fs.pathExists(config.storage.leadsFile);
    const processedExist = await fs.pathExists(config.storage.processedPostsFile);

    console.log('📁 Data Files:');
    console.log(`   • Leads file: ${leadsExist ? '✅ Found' : '❌ Not found'}`);
    console.log(`   • Processed posts: ${processedExist ? '✅ Found' : '❌ Not found'}\n`);

    if (leadsExist) {
      const leads = await fs.readJson(config.storage.leadsFile);
      console.log('🎯 Lead Statistics:');
      console.log(`   • Total leads: ${leads.length}`);
      
      if (leads.length > 0) {
        const statusCounts = {};
        const priorityCounts = {};
        let totalScore = 0;
        let recentLeads = 0;
        
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        
        leads.forEach(lead => {
          statusCounts[lead.status] = (statusCounts[lead.status] || 0) + 1;
          priorityCounts[lead.priority] = (priorityCounts[lead.priority] || 0) + 1;
          totalScore += lead.scoring.composite;
          
          if (new Date(lead.createdAt) > oneDayAgo) {
            recentLeads++;
          }
        });

        console.log(`   • Average score: ${Math.round(totalScore / leads.length)}/100`);
        console.log(`   • Recent (24h): ${recentLeads}`);
        console.log(`   • By status: ${JSON.stringify(statusCounts)}`);
        console.log(`   • By priority: ${JSON.stringify(priorityCounts)}`);

        // Show latest lead
        const latestLead = leads[leads.length - 1];
        console.log(`\n🔥 Latest Lead:`);
        console.log(`   • Author: @${latestLead.post.author.username}`);
        console.log(`   • Score: ${latestLead.scoring.composite}/100`);
        console.log(`   • Priority: ${latestLead.priority}`);
        console.log(`   • Text: "${latestLead.post.text.substring(0, 80)}..."`);
        console.log(`   • Created: ${new Date(latestLead.createdAt).toLocaleString()}`);
      }
    }

    if (processedExist) {
      const processed = await fs.readJson(config.storage.processedPostsFile);
      console.log(`\n📈 Processing Stats:`);
      console.log(`   • Posts processed: ${processed.processedIds ? processed.processedIds.length : 0}`);
      console.log(`   • Last updated: ${processed.lastUpdated ? new Date(processed.lastUpdated).toLocaleString() : 'Never'}`);
    }

    // Check LM Studio connection
    console.log('\n🤖 LM Studio Status:');
    try {
      const axios = require('axios');
      const response = await axios.post('http://127.0.0.1:1234/v1/chat/completions', {
        model: 'qwen/qwen3-32b',
        messages: [{ role: 'user', content: 'ping' }],
        max_tokens: 1
      }, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000
      });

      console.log('   • Connection: ✅ Online');
      console.log(`   • Model: ${response.data.model || 'Unknown'}`);
    } catch (error) {
      console.log('   • Connection: ❌ Offline or error');
      console.log(`   • Error: ${error.message}`);
    }

    console.log('\n⚙️ Configuration:');
    console.log(`   • Lead threshold: ${config.scoring.threshold}/100`);
    console.log(`   • Processing schedule: ${config.processing.cronSchedule}`);
    console.log(`   • Max posts per batch: ${config.processing.maxPostsPerRequest}`);
    console.log(`   • Notifications: ${config.notifications.enabled ? 'Enabled' : 'Disabled'}`);

  } catch (error) {
    console.error('❌ Error reading status:', error.message);
  }
}

showStatus();
