const ThreadsLeadGenerator = require('./main');

async function testSystem() {
  console.log('🧪 Testing Threads Lead Generation System...\n');
  
  const generator = new ThreadsLeadGenerator();
  
  try {
    // Test initialization
    console.log('1. Testing system initialization...');
    const initialized = await generator.initialize();
    
    if (initialized) {
      console.log('✅ System initialized successfully\n');
      
      // Test single run
      console.log('2. Testing single processing run...');
      await generator.runOnce();
      
      // Show stats
      console.log('\n3. Current system statistics:');
      const stats = await generator.getStats();
      console.log(JSON.stringify(stats, null, 2));
      
      console.log('\n✅ System test completed successfully!');
      console.log('\nTo start automated processing, run: npm start');
      console.log('To run once manually, run: npm start -- --once');
      
    } else {
      console.log('❌ System initialization failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('\nTroubleshooting tips:');
    console.error('1. Make sure LM Studio is running on http://127.0.0.1:1234');
    console.error('2. Check your .env file has valid Threads authentication');
    console.error('3. Ensure Qwen3 model is loaded in LM Studio');
  }
  
  process.exit(0);
}

testSystem();
