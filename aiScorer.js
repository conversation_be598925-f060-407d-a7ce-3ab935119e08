const axios = require('axios');
const config = require('./config');

class AIScorer {
  constructor() {
    this.lmStudioConfig = config.lmStudio;
    this.scoringConfig = config.scoring;
    this.lastHealthCheck = 0;
    this.healthCheckInterval = 30000; // 30 seconds
    this.isHealthy = false;
  }

  /**
   * Create the prompt for lead scoring
   */
  createScoringPrompt(post) {
    return `Score this post for lead potential. Look for people seeking web development, design, or software services.

POST:
Author: @${post.author.username} ${post.author.isVerified ? '(Verified)' : ''}
Text: "${post.text}"
Engagement: ${post.engagement.likes} likes, ${post.engagement.replies} replies

SCORING GUIDELINES:
- Website/app development requests: 80+ points
- Logo/design requests: 75+ points
- General tech help requests: 70+ points
- Business problems needing solutions: 65+ points

URGENCY (0-100): Active requests = 70+, general needs = 50+
OBTAINABILITY (0-100): Public posts asking for help = 75+, professional accounts = 65+
GENERAL SCORE (0-100): Direct service requests = 80+, business needs = 70+

JSON response only:
{
  "isLead": boolean,
  "urgency": number,
  "obtainability": number,
  "generalScore": number,
  "reasoning": "brief explanation"
}`;
  }

  /**
   * Send request to LM Studio
   */
  async queryLMStudio(prompt) {
    try {
      const response = await axios.post(this.lmStudioConfig.url, {
        model: this.lmStudioConfig.model,
        messages: [
          {
            role: "system",
            content: "You are a professional lead generation analyst. Always respond with valid JSON only."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: this.lmStudioConfig.maxTokens,
        temperature: this.lmStudioConfig.temperature,
        stream: false
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 120000 // 2 minute timeout for complex prompts
      });

      if (response.data && response.data.choices && response.data.choices[0]) {
        return response.data.choices[0].message.content.trim();
      } else {
        throw new Error('Invalid response from LM Studio');
      }
    } catch (error) {
      console.error('Error querying LM Studio:', error.message);
      throw error;
    }
  }

  /**
   * Parse and validate AI response
   */
  parseAIResponse(responseText) {
    try {
      // Clean up the response text - remove thinking tags, markdown formatting, comments
      let cleanText = responseText
        .replace(/<think>[\s\S]*?<\/think>/gi, '') // Remove thinking tags (case insensitive)
        .replace(/```json\n?/gi, '')
        .replace(/```\n?/gi, '')
        .replace(/\/\/.*$/gm, '') // Remove single-line comments
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
        .replace(/^[^{]*({[\s\S]*})[^}]*$/g, '$1') // Extract JSON object if surrounded by other text
        .trim();

      const parsed = JSON.parse(cleanText);
      
      // Validate required fields
      const required = ['isLead', 'urgency', 'obtainability', 'generalScore', 'reasoning'];
      for (const field of required) {
        if (!(field in parsed)) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Validate score ranges
      const scores = ['urgency', 'obtainability', 'generalScore'];
      for (const score of scores) {
        if (typeof parsed[score] !== 'number' || parsed[score] < 0 || parsed[score] > 100) {
          throw new Error(`Invalid score for ${score}: must be number between 0-100`);
        }
      }

      // Validate boolean
      if (typeof parsed.isLead !== 'boolean') {
        throw new Error('isLead must be boolean');
      }

      return parsed;
    } catch (error) {
      console.error('Error parsing AI response:', error.message);
      console.error('Raw response:', responseText);
      throw new Error(`Failed to parse AI response: ${error.message}`);
    }
  }

  /**
   * Calculate composite score based on weights
   */
  calculateCompositeScore(scores) {
    const weights = this.scoringConfig.weights;
    return Math.round(
      (scores.urgency * weights.urgency) +
      (scores.obtainability * weights.obtainability) +
      (scores.generalScore * weights.general)
    );
  }

  /**
   * Score a single post
   */
  async scorePost(post) {
    try {
      console.log(`Scoring post by @${post.author.username}: "${post.text.substring(0, 50)}..."`);
      
      const prompt = this.createScoringPrompt(post);
      const aiResponse = await this.queryLMStudio(prompt);
      const scores = this.parseAIResponse(aiResponse);
      
      const compositeScore = this.calculateCompositeScore(scores);
      
      const result = {
        postId: post.id,
        isLead: scores.isLead,
        scores: {
          urgency: scores.urgency,
          obtainability: scores.obtainability,
          general: scores.generalScore,
          composite: compositeScore
        },
        reasoning: scores.reasoning,
        qualifiesAsLead: scores.isLead && compositeScore >= this.scoringConfig.threshold,
        timestamp: new Date().toISOString()
      };

      console.log(`Post scored: ${compositeScore}/100 (${result.qualifiesAsLead ? 'QUALIFIED' : 'not qualified'})`);
      return result;
      
    } catch (error) {
      console.error(`Error scoring post ${post.id}:`, error.message);
      return {
        postId: post.id,
        isLead: false,
        scores: { urgency: 0, obtainability: 0, general: 0, composite: 0 },
        reasoning: `Error during scoring: ${error.message}`,
        qualifiesAsLead: false,
        timestamp: new Date().toISOString(),
        error: true
      };
    }
  }

  /**
   * Create initial lead filtering prompt for batch analysis
   */
  createLeadFilterPrompt(posts) {
    const postsArray = posts.map(post => ({
      id: post.id,
      author: post.author.username,
      verified: post.author.isVerified,
      text: post.text,
      likes: post.engagement.likes,
      replies: post.engagement.replies,
      linkPreview: post.linkPreview ? post.linkPreview.title : null
    }));

    return `Analyze these posts for business leads. Look for: job postings, service requests, startup mentions, business problems, funding discussions.

POSTS:
${JSON.stringify(postsArray, null, 2)}

Respond with JSON only:
{
  "hasLeads": boolean,
  "leadPostIds": ["id1", "id2"],
  "reasoning": "brief explanation"
}`;
  }

  /**
   * Filter posts for potential leads using batch analysis
   */
  async filterPotentialLeads(posts) {
    try {
      console.log(`🔍 Filtering ${posts.length} posts for potential leads...`);

      const prompt = this.createLeadFilterPrompt(posts);
      const aiResponse = await this.queryLMStudio(prompt);
      const filterResult = this.parseFilterResponse(aiResponse);

      if (filterResult.hasLeads && filterResult.leadPostIds.length > 0) {
        const potentialLeads = posts.filter(post =>
          filterResult.leadPostIds.includes(post.id)
        );

        console.log(`✅ Found ${potentialLeads.length} potential leads: ${filterResult.reasoning}`);
        return {
          hasLeads: true,
          posts: potentialLeads,
          reasoning: filterResult.reasoning
        };
      } else {
        console.log(`❌ No leads found: ${filterResult.reasoning}`);
        return {
          hasLeads: false,
          posts: [],
          reasoning: filterResult.reasoning
        };
      }

    } catch (error) {
      console.error('Error filtering potential leads:', error.message);
      // Fallback: treat all posts as potential leads if filtering fails
      return {
        hasLeads: true,
        posts: posts,
        reasoning: `Filter failed, processing all posts: ${error.message}`
      };
    }
  }

  /**
   * Parse and validate filter response
   */
  parseFilterResponse(responseText) {
    try {
      let cleanText = responseText
        .replace(/<think>[\s\S]*?<\/think>/gi, '') // Remove thinking tags (case insensitive)
        .replace(/```json\n?/gi, '')
        .replace(/```\n?/gi, '')
        .replace(/\/\/.*$/gm, '') // Remove single-line comments
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
        .replace(/^[^{]*({[\s\S]*})[^}]*$/g, '$1') // Extract JSON object if surrounded by other text
        .trim();
      const parsed = JSON.parse(cleanText);

      // Validate required fields
      if (!('hasLeads' in parsed) || !('leadPostIds' in parsed) || !('reasoning' in parsed)) {
        throw new Error('Missing required fields in filter response');
      }

      if (typeof parsed.hasLeads !== 'boolean') {
        throw new Error('hasLeads must be boolean');
      }

      if (!Array.isArray(parsed.leadPostIds)) {
        throw new Error('leadPostIds must be array');
      }

      return parsed;
    } catch (error) {
      console.error('Error parsing filter response:', error.message);
      console.error('Raw response:', responseText);
      throw new Error(`Failed to parse filter response: ${error.message}`);
    }
  }

  /**
   * Score a single post with individual conversation
   */
  async scorePostIndividually(post) {
    try {
      console.log(`🎯 Scoring individual post by @${post.author.username}: "${post.text.substring(0, 50)}..."`);

      const prompt = this.createScoringPrompt(post);
      const aiResponse = await this.queryLMStudio(prompt);
      const scores = this.parseAIResponse(aiResponse);

      const compositeScore = this.calculateCompositeScore(scores);

      const result = {
        postId: post.id,
        isLead: scores.isLead,
        scores: {
          urgency: scores.urgency,
          obtainability: scores.obtainability,
          general: scores.generalScore,
          composite: compositeScore
        },
        reasoning: scores.reasoning,
        qualifiesAsLead: scores.isLead && compositeScore >= this.scoringConfig.threshold,
        timestamp: new Date().toISOString()
      };

      console.log(`📊 Post scored: ${compositeScore}/100 (${result.qualifiesAsLead ? 'QUALIFIED' : 'not qualified'})`);
      return result;

    } catch (error) {
      console.error(`Error scoring post ${post.id}:`, error.message);
      return {
        postId: post.id,
        isLead: false,
        scores: { urgency: 0, obtainability: 0, general: 0, composite: 0 },
        reasoning: `Error during scoring: ${error.message}`,
        qualifiesAsLead: false,
        timestamp: new Date().toISOString(),
        error: true
      };
    }
  }

  /**
   * Process posts with two-stage filtering and individual scoring
   */
  async processPosts(posts) {
    console.log(`\n🚀 Starting two-stage lead processing for ${posts.length} posts...`);

    // Stage 1: Batch filtering for potential leads
    const filterResult = await this.filterPotentialLeads(posts);

    if (!filterResult.hasLeads) {
      console.log('🔄 No potential leads found, will retry on next cycle');
      return [];
    }

    // Stage 2: Individual detailed scoring for potential leads
    console.log(`\n🎯 Stage 2: Detailed scoring of ${filterResult.posts.length} potential leads...`);
    const results = [];

    for (const post of filterResult.posts) {
      try {
        const score = await this.scorePostIndividually(post);
        results.push(score);

        // Delay between individual scoring requests
        if (filterResult.posts.indexOf(post) < filterResult.posts.length - 1) {
          console.log('⏳ Waiting before next scoring...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        console.error(`Failed to score post ${post.id}:`, error.message);
        results.push({
          postId: post.id,
          isLead: false,
          scores: { urgency: 0, obtainability: 0, general: 0, composite: 0 },
          reasoning: `Scoring failed: ${error.message}`,
          qualifiesAsLead: false,
          timestamp: new Date().toISOString(),
          error: true
        });
      }
    }

    const qualifiedLeads = results.filter(r => r.qualifiesAsLead);
    console.log(`✅ Processing complete: ${qualifiedLeads.length}/${results.length} leads qualified`);

    return results;
  }

  /**
   * Legacy method for backward compatibility
   */
  async scorePosts(posts) {
    return await this.processPosts(posts);
  }
}

module.exports = AIScorer;
