const axios = require('axios');
const config = require('./config');

class AIScorer {
  constructor() {
    this.lmStudioConfig = config.lmStudio;
    this.scoringConfig = config.scoring;
  }

  /**
   * Create the prompt for lead scoring
   */
  createScoringPrompt(post) {
    return `You are an expert lead generation analyst. Analyze the following social media post and determine if it represents a potential business lead for my software and design agency.

POST DETAILS:
Author: @${post.author.username} (${post.author.fullName})
Verified: ${post.author.isVerified ? 'Yes' : 'No'}
Text: "${post.text}"
Engagement: ${post.engagement.likes} likes, ${post.engagement.replies} replies, ${post.engagement.reposts} reposts
${post.linkPreview ? `Link: ${post.linkPreview.title} - ${post.linkPreview.url}` : ''}

SCORING CRITERIA:
1. URGENCY (0-100): How urgent is their need? Look for time-sensitive language, deadlines, immediate problems, or pressing business needs.

2. OBTAINABILITY (0-100): How likely are they to be reachable and convertible? Consider their engagement level, follower interaction, professional presence, and openness to solutions.

3. GENERAL LEAD SCORE (0-100): Overall assessment of lead quality considering business potential, budget indicators, decision-making authority, and fit for B2B services.

INSTRUCTIONS:
- To be considered a Lead, the post must show someone actively searching for our services. It cannot be implied, it has to b
- Respond ONLY with a JSON object
- Include "isLead" (boolean), "urgency" (0-100), "obtainability" (0-100), "generalScore" (0-100), and "reasoning" (brief explanation)
- A post is considered a lead if it shows business need, problem-solving opportunity, or professional interest
- Look for: job postings, business problems, service requests, professional discussions, startup mentions, funding announcements, tool recommendations, business growth discussions
- Ignore: personal posts, entertainment, politics (unless business-related), casual conversations

Example response:
{
  "isLead": true,
  "urgency": 85,
  "obtainability": 70,
  "generalScore": 78,
  "reasoning": "User is actively seeking business solutions with time pressure, shows engagement and professional presence"
}`;
  }

  /**
   * Send request to LM Studio
   */
  async queryLMStudio(prompt) {
    try {
      const response = await axios.post(this.lmStudioConfig.url, {
        model: this.lmStudioConfig.model,
        messages: [
          {
            role: "system",
            content: "You are a professional lead generation analyst. Always respond with valid JSON only."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: this.lmStudioConfig.maxTokens,
        temperature: this.lmStudioConfig.temperature,
        stream: false
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30 second timeout
      });

      if (response.data && response.data.choices && response.data.choices[0]) {
        return response.data.choices[0].message.content.trim();
      } else {
        throw new Error('Invalid response from LM Studio');
      }
    } catch (error) {
      console.error('Error querying LM Studio:', error.message);
      throw error;
    }
  }

  /**
   * Parse and validate AI response
   */
  parseAIResponse(responseText) {
    try {
      // Clean up the response text - remove any markdown formatting
      let cleanText = responseText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      
      const parsed = JSON.parse(cleanText);
      
      // Validate required fields
      const required = ['isLead', 'urgency', 'obtainability', 'generalScore', 'reasoning'];
      for (const field of required) {
        if (!(field in parsed)) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Validate score ranges
      const scores = ['urgency', 'obtainability', 'generalScore'];
      for (const score of scores) {
        if (typeof parsed[score] !== 'number' || parsed[score] < 0 || parsed[score] > 100) {
          throw new Error(`Invalid score for ${score}: must be number between 0-100`);
        }
      }

      // Validate boolean
      if (typeof parsed.isLead !== 'boolean') {
        throw new Error('isLead must be boolean');
      }

      return parsed;
    } catch (error) {
      console.error('Error parsing AI response:', error.message);
      console.error('Raw response:', responseText);
      throw new Error(`Failed to parse AI response: ${error.message}`);
    }
  }

  /**
   * Calculate composite score based on weights
   */
  calculateCompositeScore(scores) {
    const weights = this.scoringConfig.weights;
    return Math.round(
      (scores.urgency * weights.urgency) +
      (scores.obtainability * weights.obtainability) +
      (scores.generalScore * weights.general)
    );
  }

  /**
   * Score a single post
   */
  async scorePost(post) {
    try {
      console.log(`Scoring post by @${post.author.username}: "${post.text.substring(0, 50)}..."`);
      
      const prompt = this.createScoringPrompt(post);
      const aiResponse = await this.queryLMStudio(prompt);
      const scores = this.parseAIResponse(aiResponse);
      
      const compositeScore = this.calculateCompositeScore(scores);
      
      const result = {
        postId: post.id,
        isLead: scores.isLead,
        scores: {
          urgency: scores.urgency,
          obtainability: scores.obtainability,
          general: scores.generalScore,
          composite: compositeScore
        },
        reasoning: scores.reasoning,
        qualifiesAsLead: scores.isLead && compositeScore >= this.scoringConfig.threshold,
        timestamp: new Date().toISOString()
      };

      console.log(`Post scored: ${compositeScore}/100 (${result.qualifiesAsLead ? 'QUALIFIED' : 'not qualified'})`);
      return result;
      
    } catch (error) {
      console.error(`Error scoring post ${post.id}:`, error.message);
      return {
        postId: post.id,
        isLead: false,
        scores: { urgency: 0, obtainability: 0, general: 0, composite: 0 },
        reasoning: `Error during scoring: ${error.message}`,
        qualifiesAsLead: false,
        timestamp: new Date().toISOString(),
        error: true
      };
    }
  }

  /**
   * Score multiple posts
   */
  async scorePosts(posts) {
    const results = [];
    
    for (const post of posts) {
      try {
        const score = await this.scorePost(post);
        results.push(score);
        
        // Add delay between requests to avoid overwhelming LM Studio
        if (posts.indexOf(post) < posts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`Failed to score post ${post.id}:`, error.message);
        results.push({
          postId: post.id,
          isLead: false,
          scores: { urgency: 0, obtainability: 0, general: 0, composite: 0 },
          reasoning: `Scoring failed: ${error.message}`,
          qualifiesAsLead: false,
          timestamp: new Date().toISOString(),
          error: true
        });
      }
    }
    
    return results;
  }
}

module.exports = AIScorer;
