const cron = require('cron');
const ThreadsClient = require('./threadsClient');
const AIScorer = require('./aiScorer');
const LeadManager = require('./leadManager');
const NotificationService = require('./notificationService');
const config = require('./config');

class ThreadsLeadGenerator {
  constructor() {
    this.threadsClient = new ThreadsClient();
    this.aiScorer = new AIScorer();
    this.leadManager = new LeadManager();
    this.notificationService = new NotificationService();
    this.isRunning = false;
    this.stats = {
      totalPostsProcessed: 0,
      totalLeadsFound: 0,
      lastRunTime: null,
      errors: 0
    };
  }

  /**
   * Initialize the system
   */
  async initialize() {
    try {
      console.log('🚀 Initializing Threads Lead Generator...');
      
      // Test LM Studio connection
      await this.testLMStudioConnection();
      
      // Test notification system
      await this.notificationService.testNotification();
      
      console.log('✅ System initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Initialization failed:', error.message);
      await this.notificationService.sendSystemNotification(
        'Initialization Failed',
        error.message,
        'error'
      );
      return false;
    }
  }

  /**
   * Test LM Studio connection
   */
  async testLMStudioConnection() {
    try {
      console.log('Testing LM Studio connection...');
      
      const testPost = {
        id: 'test',
        text: 'Looking for a marketing consultant to help grow my startup',
        author: { username: 'testuser', fullName: 'Test User', isVerified: false },
        engagement: { likes: 10, replies: 2, reposts: 1 }
      };

      await this.aiScorer.scorePost(testPost);
      console.log('✅ LM Studio connection successful');
    } catch (error) {
      throw new Error(`LM Studio connection failed: ${error.message}`);
    }
  }

  /**
   * Main processing loop with continuous lead hunting
   */
  async processNewPosts() {
    if (this.isRunning) {
      console.log('⏳ Processing already in progress, skipping...');
      return;
    }

    this.isRunning = true;
    console.log('\n🔄 Starting lead hunting cycle...');

    try {
      let totalPostsProcessed = 0;
      let totalLeadsFound = 0;
      let cycleCount = 0;
      const maxCycles = 10; // Prevent infinite loops

      while (cycleCount < maxCycles) {
        cycleCount++;
        console.log(`\n🎯 Cycle ${cycleCount}: Fetching new posts...`);

        // Fetch new posts from Threads
        const { posts, nextCursor, hasMore } = await this.threadsClient.getNewPosts();

        if (posts.length === 0) {
          console.log('📭 No new posts found, ending cycle');
          break;
        }

        console.log(`📥 Fetched ${posts.length} posts`);

        // Filter out already processed posts
        const newPosts = await this.leadManager.filterNewPosts(posts);

        if (newPosts.length === 0) {
          console.log('✅ All posts already processed');
          // Update cursor and continue if there are more posts
          if (nextCursor && hasMore) {
            this.threadsClient.updateCursor(nextCursor);
            continue;
          } else {
            break;
          }
        }

        console.log(`🆕 Analyzing ${newPosts.length} new posts for leads...`);

        // Two-stage AI processing: filter then score
        const scores = await this.aiScorer.processPosts(newPosts);

        // Mark all posts as processed (even if no leads found)
        const postIds = newPosts.map(post => post.id);
        await this.leadManager.markPostsAsProcessed(postIds);

        // Update cursor for next fetch
        if (nextCursor) {
          this.threadsClient.updateCursor(nextCursor);
        }

        totalPostsProcessed += newPosts.length;

        // Check if we found any qualified leads
        const qualifiedScores = scores.filter(score => score.qualifiesAsLead);

        if (qualifiedScores.length > 0) {
          console.log(`🎉 Found ${qualifiedScores.length} qualified leads! Processing...`);

          // Get the actual posts for qualified leads
          const qualifiedPosts = newPosts.filter(post =>
            qualifiedScores.some(score => score.postId === post.id)
          );

          // Add qualified leads to storage
          const newLeads = await this.leadManager.addLeads(qualifiedPosts, qualifiedScores);

          // Send notifications for new leads
          if (newLeads.length > 0) {
            await this.sendLeadNotifications(newLeads);
          }

          totalLeadsFound += newLeads.length;

          console.log(`✅ Cycle complete: Found ${newLeads.length} new leads!`);
          break; // Exit the loop when we find leads
        } else {
          console.log(`🔄 No qualified leads in this batch, continuing search...`);

          // If no more posts available, break
          if (!hasMore || !nextCursor) {
            console.log('📭 No more posts available, ending search');
            break;
          }

          // Small delay before next cycle
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }

      // Update stats
      this.updateStats(totalPostsProcessed, totalLeadsFound);

      if (totalLeadsFound > 0) {
        console.log(`🎯 Lead hunting successful: ${totalLeadsFound} leads found after processing ${totalPostsProcessed} posts`);
      } else {
        console.log(`🔍 Lead hunting complete: No leads found in ${totalPostsProcessed} posts across ${cycleCount} cycles`);
      }

    } catch (error) {
      console.error('❌ Error during processing:', error.message);
      this.stats.errors++;

      await this.notificationService.sendSystemNotification(
        'Processing Error',
        error.message,
        'error'
      );
    } finally {
      this.isRunning = false;
      this.stats.lastRunTime = new Date().toISOString();
    }
  }

  /**
   * Send notifications for new leads
   */
  async sendLeadNotifications(leads) {
    try {
      // Send individual notifications for high-priority leads
      const highPriorityLeads = leads.filter(lead => 
        lead.priority === 'critical' || lead.priority === 'high'
      );

      for (const lead of highPriorityLeads) {
        await this.notificationService.sendLeadNotification(lead);
        // Small delay between notifications
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Send batch notification if there are multiple leads
      if (leads.length > 1) {
        await this.notificationService.sendBatchNotification(leads);
      }
      
    } catch (error) {
      console.error('Error sending lead notifications:', error.message);
    }
  }

  /**
   * Update processing statistics
   */
  updateStats(postsProcessed, leadsFound) {
    this.stats.totalPostsProcessed += postsProcessed;
    this.stats.totalLeadsFound += leadsFound;
  }

  /**
   * Get current statistics
   */
  async getStats() {
    const leadStats = await this.leadManager.getLeadStats();
    return {
      ...this.stats,
      leads: leadStats
    };
  }

  /**
   * Start automated processing with cron schedule
   */
  startAutomatedProcessing() {
    console.log(`⏰ Starting automated processing (${config.processing.cronSchedule})`);
    
    const job = new cron.CronJob(
      config.processing.cronSchedule,
      () => this.processNewPosts(),
      null,
      true,
      'America/New_York'
    );

    console.log('🤖 Automated processing started');
    return job;
  }

  /**
   * Run once manually
   */
  async runOnce() {
    console.log('🎯 Running manual processing cycle...');
    await this.processNewPosts();
  }

  /**
   * Send daily summary
   */
  async sendDailySummary() {
    try {
      const stats = await this.getStats();
      await this.notificationService.sendDailySummary(stats.leads);
    } catch (error) {
      console.error('Error sending daily summary:', error.message);
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🛑 Shutting down Threads Lead Generator...');
    this.isRunning = false;
    console.log('✅ Shutdown complete');
  }
}

// Main execution
async function main() {
  const generator = new ThreadsLeadGenerator();
  
  // Handle process termination
  process.on('SIGINT', async () => {
    await generator.shutdown();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    await generator.shutdown();
    process.exit(0);
  });

  // Initialize system
  const initialized = await generator.initialize();
  if (!initialized) {
    console.error('Failed to initialize system');
    process.exit(1);
  }

  // Check command line arguments
  const args = process.argv.slice(2);
  
  if (args.includes('--once')) {
    // Run once and exit
    await generator.runOnce();
    process.exit(0);
  } else if (args.includes('--stats')) {
    // Show stats and exit
    const stats = await generator.getStats();
    console.log('\n📊 Current Statistics:');
    console.log(JSON.stringify(stats, null, 2));
    process.exit(0);
  } else if (args.includes('--summary')) {
    // Send daily summary and exit
    await generator.sendDailySummary();
    process.exit(0);
  } else {
    // Start automated processing
    generator.startAutomatedProcessing();
    
    // Keep the process running
    console.log('🔄 System running... Press Ctrl+C to stop');
    
    // Optional: run once immediately
    if (args.includes('--immediate')) {
      setTimeout(() => generator.runOnce(), 5000);
    }
  }
}

// Run the application
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = ThreadsLeadGenerator;
