# Threads Lead Generation System

An AI-powered lead generation system that monitors Threads posts, analyzes them using a local Qwen3 model via LM Studio, and identifies high-quality business leads with automated scoring and notifications.

## Features

- 🔍 **Automated Post Monitoring**: Continuously fetches new posts from Threads API
- 🤖 **AI-Powered Lead Scoring**: Uses local Qwen3-32B model to score leads on urgency, obtainability, and general quality
- 📊 **Intelligent Filtering**: Only processes new posts and avoids duplicates
- 🚨 **Real-time Notifications**: Desktop notifications for high-priority leads
- 💾 **Persistent Storage**: Saves leads and tracks processed posts
- 📈 **Analytics & Stats**: Comprehensive lead statistics and reporting
- ⏰ **Automated Scheduling**: Runs on configurable cron schedule

## Prerequisites

1. **Node.js** (v16 or higher)
2. **LM Studio** running locally with Qwen3-32B model at `http://127.0.0.1:1234`
3. **Threads Account** with valid authentication cookies

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment template:
   ```bash
   cp .env.example .env
   ```

4. Configure your Threads authentication in `.env` file

## Configuration

### Threads Authentication

1. Log into Threads in your browser
2. Open Developer Tools (F12)
3. Go to Network tab and refresh the page
4. Find a request to `threads.com/graphql/query`
5. Copy the `Cookie` header value to `THREADS_COOKIES` in `.env`
6. Copy the `x-csrftoken` header value to `THREADS_CSRF_TOKEN` in `.env`

### LM Studio Setup

1. Install and run LM Studio
2. Load the Qwen3-32B model
3. Start the local server at `http://127.0.0.1:1234`
4. Ensure the model is responding to API requests

### Scoring Configuration

Edit `config.js` to adjust:
- Lead score threshold (default: 70/100)
- Scoring weights for urgency, obtainability, and general score
- Processing frequency and batch sizes

## Usage

### Production Mode (Recommended)

Start the full production system with automated lead hunting:
```bash
node start-production.js
```

### Manual Commands

Run once and exit:
```bash
npm run once
```

Start automated processing:
```bash
npm start
```

View current statistics:
```bash
npm run stats
```

Send daily summary notification:
```bash
npm run summary
```

## How It Works

1. **Post Fetching**: Connects to Threads API using your authentication
2. **Deduplication**: Filters out already processed posts
3. **AI Analysis**: Sends each post to local Qwen3 model with detailed scoring prompt
4. **Lead Scoring**: Evaluates posts on three criteria:
   - **Urgency** (0-100): Time-sensitive needs, deadlines, immediate problems
   - **Obtainability** (0-100): Likelihood of being reachable and convertible
   - **General Score** (0-100): Overall business potential and lead quality
5. **Qualification**: Posts scoring above threshold become qualified leads
6. **Storage**: Saves leads with metadata, tags, and priority levels
7. **Notifications**: Sends desktop alerts for high-priority leads

## Lead Scoring Criteria

The AI analyzes posts for:
- Business problems and pain points
- Service requests and help-seeking behavior
- Startup mentions and funding announcements
- Job postings and hiring needs
- Professional discussions and tool recommendations
- Time-sensitive language and urgency indicators
- Author credibility and engagement levels

## File Structure

```
├── main.js                 # Main orchestrator
├── threadsClient.js        # Threads API interface
├── aiScorer.js            # LM Studio AI scoring
├── leadManager.js         # Lead storage and management
├── notificationService.js # Desktop notifications
├── config.js              # Configuration settings
├── package.json           # Dependencies
├── .env                   # Environment variables
├── data/                  # Generated data directory
│   ├── leads.json         # Qualified leads storage
│   └── processed_posts.json # Tracking processed posts
└── README.md              # This file
```

## Data Storage

- **Leads**: Stored in `data/leads.json` with full post content, scoring details, and metadata
- **Processed Posts**: Tracked in `data/processed_posts.json` to avoid reprocessing
- **Lead Status**: Each lead has status (new, contacted, qualified, closed)
- **Priority Levels**: critical, high, medium, low based on composite score

## Customization

### Scoring Prompt

Edit the `createScoringPrompt()` method in `aiScorer.js` to customize how the AI evaluates posts.

### Lead Tags

Modify `generateTags()` in `leadManager.js` to add custom tagging logic.

### Notification Settings

Update `config.js` notification settings for custom alerts and timing.

### Processing Schedule

Change the cron schedule in `config.js` to adjust processing frequency.

## Troubleshooting

### Common Issues

1. **LM Studio Connection Failed**
   - Ensure LM Studio is running on port 1234
   - Check that Qwen3 model is loaded and responding
   - Verify no firewall blocking localhost connections

2. **Threads API Authentication Error**
   - Update cookies - they expire regularly
   - Check CSRF token matches current session
   - Ensure account has access to Threads

3. **No Notifications**
   - Check notification permissions in system settings
   - Verify `node-notifier` works on your platform
   - Enable notifications in `config.js`

### Debug Mode

Add console logging by setting environment variable:
```bash
DEBUG=true npm start
```

## Security Notes

- Keep your `.env` file secure and never commit it to version control
- Threads cookies contain sensitive authentication data
- Consider using a dedicated Threads account for automation
- Monitor API usage to avoid rate limiting

## License

MIT License - see LICENSE file for details
