const axios = require('axios');
const qs = require('qs');
let data = qs.stringify({
  'av': '17841463402045411',
  '__user': '0',
  '__a': '1',
  '__req': 'n',
  '__hs': '20246.HYP:barcelona_web_pkg.2.1...0',
  'dpr': '2',
  '__ccg': 'UNKNOWN',
  '__rev': '1023607972',
  '__s': '43hayb:4zkpc2:ykux39',
  '__hsi': '7513257824855179392',
  '__dyn': '7xeUmwlEnwn8K2Wmh0no6u5U4e0yoW3q32360CEbo1nEhw2nVE4W0qa0FE2awgo9oO0n24oaEd82lwv89k2C1Fwc60D85m1mzXwae4UaEW0Loco5G0zK5o4q0HU1IEGdwtU2ewbS1LwTwKG0hq1Iwqo9EpwUwiQ0xEaUuwhUyu4Q2-qfwGxW0C85O1tK1Uw',
  '__csr': 'ggkAQytEL5ANtl4XBKGO4damNemXxaLwxCzohDzEjV9UGmBF1d4Hg01pmE2Jy4bDhU3dw2A9Q0KMKwA5IMgxe584y0SE234388ox0Ih2N02mw4ix11y1mgeA7om5o2JK0sS16w2iox5gckmU1bEeE0Am0U4qUtwaq0Eo5V0a-1Uooxp6kEc43rc6NVl2obU8k08dwlUeE4i12y8y0rW8wVxG13D868beWG7k4Wxd1ongC5N01C80jPDkFxxQbw08Ee8x55g',
  '__hsdp': 'gbff_T9NFgVu95gb8g2Vxb3hmF108oiagNklA1-ggyIjGCkZ942r3FWEAAqbAmoZEoby3c9gOVy6iOy8uw_S9gijw8Cdwl86QxS3qm1Aw4mwHwTw7cwgobUmxe6UO',
  '__hblp': '1B04Jg8ei2GbzEN6z84q7u8g7qewEG3648Km7Ef8Xxe2W2yu5Eog8ofQdwhEpyEhw5owuo7a49U4erDLwExS68G2ecyUhgcK6QqvxiexS4UmAxe5oswh8y',
  '__comet_req': '29',
  'fb_dtsg': 'NAfvY6P7bXufib8whNJ-k2LNSaFGvSuCq65tobbM_ByjQdc7kfDf1AA:17853667720085245:1749316588',
  'jazoest': '26283',
  'lsd': 'EVbdoebc_eC2ziHDPpdwCI',
  '__spin_r': '1023607972',
  '__spin_b': 'trunk',
  '__spin_t': '1749316655',
  '__jssesw': '1',
  '__crn': 'comet.threads.BarcelonaHomeRoute',
  'fb_api_caller_class': 'RelayModern',
  'fb_api_req_friendly_name': 'BarcelonaFeedPaginationDirectQuery',
  'variables': '{"after":"GgYYYQDgETkTbnakMgKmuGEvr6Yygle122aApjIG0zirpjumMmkzJCnRGAAAmrTEElilpjKTqjiMT72mMpRyMuRIRaUy1WKpZYR9pjLZ8L9E8A2lMtqhNVWEXKUyvl6EhyzYpDIWBBgQY29sZF9zdGFydF9mZXRjaBbAn6-26WUW8roFOwGLFHJlcGx5X3Vwc2VsbF9pbl9mZWVkAYYKbGFzdF9pbmRleAwA","before":null,"data":{"feed_view_info":"[]","pagination_source":"text_post_feed_threads","reason":"pagination"},"first":10,"last":null,"variant":"for_you","__relay_internal__pv__BarcelonaIsLoggedInrelayprovider":true,"__relay_internal__pv__BarcelonaHasSelfReplyContextrelayprovider":false,"__relay_internal__pv__BarcelonaHasInlineReplyComposerrelayprovider":true,"__relay_internal__pv__BarcelonaIsSearchDiscoveryEnabledrelayprovider":true,"__relay_internal__pv__BarcelonaOptionalCookiesEnabledrelayprovider":true,"__relay_internal__pv__BarcelonaHasSpoilerStylingInforelayprovider":false,"__relay_internal__pv__BarcelonaQuotedPostUFIEnabledrelayprovider":false,"__relay_internal__pv__BarcelonaIsCrawlerrelayprovider":false,"__relay_internal__pv__BarcelonaHasDisplayNamesrelayprovider":false,"__relay_internal__pv__BarcelonaCanSeeSponsoredContentrelayprovider":false,"__relay_internal__pv__BarcelonaShouldShowFediverseM075Featuresrelayprovider":true,"__relay_internal__pv__BarcelonaImplicitTrendsGKrelayprovider":false,"__relay_internal__pv__BarcelonaIsInternalUserrelayprovider":false}',
  'server_timestamps': 'true',
  'doc_id': '23870540145941724' 
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://www.threads.com/graphql/query',
  headers: { 
    'accept': '*/*', 
    'accept-language': 'en-US,en;q=0.9,ro;q=0.8', 
    'content-type': 'application/x-www-form-urlencoded', 
    'origin': 'https://www.threads.com', 
    'priority': 'u=1, i', 
    'referer': 'https://www.threads.com/', 
    'sec-ch-prefers-color-scheme': 'dark', 
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"', 
    'sec-ch-ua-full-version-list': '"Google Chrome";v="137.0.7151.69", "Chromium";v="137.0.7151.69", "Not/A)Brand";v="24.0.0.0"', 
    'sec-ch-ua-mobile': '?0', 
    'sec-ch-ua-model': '""', 
    'sec-ch-ua-platform': '"macOS"', 
    'sec-ch-ua-platform-version': '"15.2.0"', 
    'sec-fetch-dest': 'empty', 
    'sec-fetch-mode': 'cors', 
    'sec-fetch-site': 'same-origin', 
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36', 
    'x-asbd-id': '359341', 
    'x-bloks-version-id': 'd76e71786ecd1b115d8db5725ebf6e695a1b7c899b17fe27d8b50d4707ee022f', 
    'x-csrftoken': '4w2XtJVnDTA7yzC8YntoJ6CpqWas3d25', 
    'x-fb-friendly-name': 'BarcelonaFeedPaginationDirectQuery', 
    'x-fb-lsd': 'EVbdoebc_eC2ziHDPpdwCI', 
    'x-ig-app-id': '238260118697367', 
    'x-root-field-name': 'xdt_api__v1__feed__text_post_app_timeline__connection', 
    'Cookie': 'cb=1_2025_06_07_; mid=aERk1wAEAAFntf00XXmsi3i-lLYK; ig_did=DE93F21A-EB48-4C3B-B5D4-76B78A24434E; csrftoken=4w2XtJVnDTA7yzC8YntoJ6CpqWas3d25; ds_user_id=63483289401; sessionid=63483289401%3AzjDrfwBklBAeYn%3A29%3AAYeqXHeHEvIuz8_al_F13e8_RvpSCvQObD9XHrvl6A; rur="NCG\\05463483289401\\0541780852662:01fee19de25f0da8e57d1bfbecc6cda75f079b3b2086af25e4ddf424d34852fc1c7c86e8"; csrftoken=4w2XtJVnDTA7yzC8YntoJ6CpqWas3d25; ds_user_id=63483289401; rur="NCG\\05463483289401\\0541780853324:01fe0fa7c46f2c6e2e0dbfd58f4519913faf2553f9a2fb7c2409e616618b6f35b3a7e8cc"'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});
