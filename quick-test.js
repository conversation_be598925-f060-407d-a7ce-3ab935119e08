const AIScorer = require('./aiScorer');

async function quickTest() {
  console.log('🧪 Quick Test of Optimized AI Scoring...\n');
  
  const aiScorer = new AIScorer();
  
  // Test posts with clear business content
  const testPosts = [
    {
      id: 'test1',
      text: 'Looking for a marketing consultant to help grow my startup. Budget is $5k/month.',
      author: { username: 'startup_founder', fullName: '<PERSON>', isVerified: false },
      engagement: { likes: 25, replies: 8, reposts: 3 },
      linkPreview: null
    },
    {
      id: 'test2', 
      text: 'Just had the best pizza ever! 🍕 #foodie',
      author: { username: 'pizza_lover', fullName: '<PERSON>', isVerified: false },
      engagement: { likes: 12, replies: 2, reposts: 0 },
      linkPreview: null
    },
    {
      id: 'test3',
      text: 'Our SaaS platform needs a new UI/UX designer. Remote work, competitive salary. DM me!',
      author: { username: 'tech_ceo', fullName: 'Tech CEO', isVerified: true },
      engagement: { likes: 45, replies: 15, reposts: 8 },
      linkPreview: null
    }
  ];

  try {
    console.log('🔍 Stage 1: Testing batch lead filtering...');
    const filterResult = await aiScorer.filterPotentialLeads(testPosts);
    
    if (filterResult.hasLeads) {
      console.log(`✅ Filter found ${filterResult.posts.length} potential leads`);
      console.log(`📝 Reasoning: ${filterResult.reasoning}\n`);
      
      console.log('🎯 Stage 2: Testing individual scoring...');
      for (const post of filterResult.posts) {
        const score = await aiScorer.scorePostIndividually(post);
        console.log(`📊 Post "${post.text.substring(0, 30)}..." scored: ${score.scores.composite}/100`);
        console.log(`   Qualified: ${score.qualifiesAsLead ? 'YES' : 'NO'}`);
        console.log(`   Reasoning: ${score.reasoning}\n`);
      }
    } else {
      console.log(`❌ No leads found: ${filterResult.reasoning}`);
    }
    
    console.log('✅ Quick test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('timeout')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check if LM Studio is running and responsive');
      console.log('2. Try reducing the model temperature or max_tokens');
      console.log('3. Ensure the Qwen3 model is properly loaded');
    }
  }
}

quickTest();
